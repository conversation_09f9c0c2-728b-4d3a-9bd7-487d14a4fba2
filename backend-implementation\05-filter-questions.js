// GET /api/tests/questions/filter
// Enhanced with advanced filtering, search, and intelligent suggestions

exports.filterQuestions = async (req, res) => {
  try {
    const {
      searchTerm,
      category,
      difficulty,
      type,
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      minPoints,
      maxPoints,
      createdAfter,
      createdBefore,
      tags,
      excludeUsedInTest,
      includeInactive = false,
      createdBy
    } = req.query;

    const companyId = req.user.companyId;

    // Build comprehensive filter
    const filter = await buildAdvancedFilter({
      companyId,
      searchTerm,
      category,
      difficulty,
      type,
      minPoints,
      maxPoints,
      createdAfter,
      createdBefore,
      tags,
      excludeUsedInTest,
      includeInactive,
      createdBy
    });

    // Setup pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const limitNum = parseInt(limit);

    // Build aggregation pipeline for enhanced search
    const pipeline = [
      { $match: filter },
      {
        $lookup: {
          from: 'users',
          localField: 'createdBy',
          foreignField: '_id',
          as: 'createdBy',
          pipeline: [{ $project: { name: 1, email: 1 } }]
        }
      },
      { $unwind: { path: '$createdBy', preserveNullAndEmptyArrays: true } }
    ];

    // Add search scoring for text search
    if (searchTerm) {
      pipeline.unshift({
        $addFields: {
          searchScore: {
            $add: [
              // Question text relevance
              {
                $cond: [
                  { $regexMatch: { input: '$questionText', regex: searchTerm, options: 'i' } },
                  10,
                  0
                ]
              },
              // Category relevance
              {
                $cond: [
                  { $regexMatch: { input: '$category', regex: searchTerm, options: 'i' } },
                  5,
                  0
                ]
              },
              // Explanation relevance
              {
                $cond: [
                  { $regexMatch: { input: '$explanation', regex: searchTerm, options: 'i' } },
                  3,
                  0
                ]
              }
            ]
          }
        }
      });
    }

    // Add sorting
    const sortStage = buildSortStage(sortBy, sortOrder, searchTerm);
    pipeline.push({ $sort: sortStage });

    // Execute aggregation with pagination
    const [questions, totalCount] = await Promise.all([
      Question.aggregate([
        ...pipeline,
        { $skip: skip },
        { $limit: limitNum }
      ]),
      Question.aggregate([
        ...pipeline,
        { $count: 'total' }
      ])
    ]);

    const total = totalCount[0]?.total || 0;

    // Enhanced questions with metadata
    const enhancedQuestions = await enhanceQuestionsWithAnalytics(questions);

    // Get filter analytics
    const filterAnalytics = await getFilterAnalytics(companyId, filter);

    // Get search suggestions
    const suggestions = await getSearchSuggestions(companyId, searchTerm, filter);

    // Build pagination info
    const pagination = buildPaginationInfo(page, limitNum, total);

    res.json({
      success: true,
      questions: enhancedQuestions,
      pagination,
      analytics: filterAnalytics,
      suggestions,
      appliedFilters: {
        searchTerm,
        category,
        difficulty,
        type,
        pointsRange: minPoints || maxPoints ? { min: minPoints, max: maxPoints } : null,
        dateRange: createdAfter || createdBefore ? { after: createdAfter, before: createdBefore } : null,
        tags: tags ? tags.split(',') : null,
        excludeUsedInTest,
        includeInactive: includeInactive === 'true'
      },
      metadata: {
        searchPerformed: !!searchTerm,
        filtersApplied: Object.keys(req.query).length - 2, // Exclude page and limit
        responseTime: Date.now() - req.startTime,
        cacheKey: generateCacheKey(req.query)
      }
    });

  } catch (error) {
    console.error('Filter questions error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// Build advanced filter object
async function buildAdvancedFilter({
  companyId,
  searchTerm,
  category,
  difficulty,
  type,
  minPoints,
  maxPoints,
  createdAfter,
  createdBefore,
  tags,
  excludeUsedInTest,
  includeInactive,
  createdBy
}) {
  const filter = {
    companyId: new mongoose.Types.ObjectId(companyId)
  };

  // Active status filter
  if (includeInactive !== 'true') {
    filter.isActive = true;
  }

  // Text search with multiple fields
  if (searchTerm) {
    const searchRegex = new RegExp(searchTerm, 'i');
    filter.$or = [
      { questionText: searchRegex },
      { category: searchRegex },
      { explanation: searchRegex },
      { 'options.text': searchRegex }
    ];
  }

  // Category filter (case insensitive)
  if (category) {
    filter.category = new RegExp(`^${category}$`, 'i');
  }

  // Difficulty filter
  if (difficulty) {
    if (difficulty.includes(',')) {
      filter.difficulty = { $in: difficulty.split(',') };
    } else {
      filter.difficulty = difficulty;
    }
  }

  // Question type filter
  if (type) {
    if (type.includes(',')) {
      filter.questionType = { $in: type.split(',') };
    } else {
      filter.questionType = type;
    }
  }

  // Points range filter
  if (minPoints || maxPoints) {
    filter.points = {};
    if (minPoints) filter.points.$gte = parseInt(minPoints);
    if (maxPoints) filter.points.$lte = parseInt(maxPoints);
  }

  // Date range filter
  if (createdAfter || createdBefore) {
    filter.createdAt = {};
    if (createdAfter) filter.createdAt.$gte = new Date(createdAfter);
    if (createdBefore) filter.createdAt.$lte = new Date(createdBefore);
  }

  // Tags filter (if you have tags field)
  if (tags) {
    const tagArray = tags.split(',').map(tag => tag.trim());
    filter.tags = { $in: tagArray };
  }

  // Created by filter
  if (createdBy) {
    filter.createdBy = new mongoose.Types.ObjectId(createdBy);
  }

  // Exclude questions used in specific test
  if (excludeUsedInTest) {
    const usedQuestions = await Test.findById(excludeUsedInTest)
      .select('questions.questionId')
      .lean();
    
    if (usedQuestions) {
      const usedQuestionIds = usedQuestions.questions.map(q => q.questionId);
      filter._id = { $nin: usedQuestionIds };
    }
  }

  return filter;
}

// Build sort stage for aggregation
function buildSortStage(sortBy, sortOrder, searchTerm) {
  const order = sortOrder === 'asc' ? 1 : -1;
  
  const sortOptions = {
    'createdAt': { createdAt: order },
    'difficulty': { difficulty: order, createdAt: -1 },
    'type': { questionType: order, createdAt: -1 },
    'points': { points: order, createdAt: -1 },
    'category': { category: order, createdAt: -1 },
    'relevance': searchTerm ? { searchScore: -1, createdAt: -1 } : { createdAt: -1 }
  };

  return sortOptions[sortBy] || sortOptions['createdAt'];
}

// Enhanced questions with analytics
async function enhanceQuestionsWithAnalytics(questions) {
  return await Promise.all(
    questions.map(async (question) => {
      // Get usage statistics
      const usageStats = await getQuestionUsageStats(question._id);
      
      // Get performance metrics
      const performanceStats = await getQuestionPerformanceStats(question._id);

      return {
        ...question,
        usageStats,
        performanceStats,
        metadata: {
          difficultyColor: getDifficultyColor(question.difficulty),
          typeIcon: getTypeIcon(question.questionType),
          estimatedTime: getEstimatedTime(question.questionType, question.difficulty),
          complexity: calculateComplexity(question)
        }
      };
    })
  );
}

// Get question usage statistics
async function getQuestionUsageStats(questionId) {
  try {
    const stats = await Test.aggregate([
      { $unwind: '$questions' },
      { $match: { 'questions.questionId': questionId } },
      {
        $group: {
          _id: null,
          usedInTests: { $sum: 1 },
          totalAttempts: { $sum: { $size: '$participants' } },
          avgPointsAssigned: { $avg: '$questions.points' }
        }
      }
    ]);

    return stats[0] || { usedInTests: 0, totalAttempts: 0, avgPointsAssigned: 0 };
  } catch (error) {
    return { usedInTests: 0, totalAttempts: 0, avgPointsAssigned: 0 };
  }
}

// Get question performance statistics
async function getQuestionPerformanceStats(questionId) {
  try {
    // This would require a TestSubmission model with individual question responses
    const stats = await TestSubmission.aggregate([
      { $unwind: '$responses' },
      { $match: { 'responses.questionId': questionId } },
      {
        $group: {
          _id: null,
          totalResponses: { $sum: 1 },
          correctResponses: {
            $sum: { $cond: ['$responses.isCorrect', 1, 0] }
          },
          avgTimeSpent: { $avg: '$responses.timeSpent' }
        }
      },
      {
        $addFields: {
          successRate: {
            $multiply: [
              { $divide: ['$correctResponses', '$totalResponses'] },
              100
            ]
          }
        }
      }
    ]);

    return stats[0] || { 
      totalResponses: 0, 
      correctResponses: 0, 
      successRate: 0, 
      avgTimeSpent: 0 
    };
  } catch (error) {
    return { totalResponses: 0, correctResponses: 0, successRate: 0, avgTimeSpent: 0 };
  }
}

// Get filter analytics
async function getFilterAnalytics(companyId, filter) {
  try {
    const analytics = await Question.aggregate([
      { $match: filter },
      {
        $group: {
          _id: null,
          totalQuestions: { $sum: 1 },
          avgPoints: { $avg: '$points' },
          difficultyBreakdown: {
            $push: '$difficulty'
          },
          typeBreakdown: {
            $push: '$questionType'
          },
          categoryBreakdown: {
            $push: '$category'
          }
        }
      }
    ]);

    if (!analytics[0]) return null;

    const data = analytics[0];
    
    return {
      totalQuestions: data.totalQuestions,
      avgPoints: Math.round(data.avgPoints * 100) / 100,
      distributions: {
        difficulty: getDistribution(data.difficultyBreakdown),
        type: getDistribution(data.typeBreakdown),
        category: getDistribution(data.categoryBreakdown)
      }
    };
  } catch (error) {
    return null;
  }
}

// Get search suggestions
async function getSearchSuggestions(companyId, searchTerm, currentFilter) {
  if (!searchTerm || searchTerm.length < 2) return [];

  try {
    // Get similar categories
    const categoryMatches = await Question.distinct('category', {
      companyId: new mongoose.Types.ObjectId(companyId),
      category: new RegExp(searchTerm, 'i'),
      isActive: true
    });

    // Get related terms from question text
    const relatedTerms = await Question.aggregate([
      {
        $match: {
          companyId: new mongoose.Types.ObjectId(companyId),
          questionText: new RegExp(searchTerm, 'i'),
          isActive: true
        }
      },
      { $limit: 10 },
      {
        $project: {
          words: {
            $split: [
              { $toLower: '$questionText' },
              ' '
            ]
          }
        }
      },
      { $unwind: '$words' },
      {
        $match: {
          words: new RegExp(searchTerm, 'i'),
          words: { $ne: searchTerm.toLowerCase() }
        }
      },
      {
        $group: {
          _id: '$words',
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } },
      { $limit: 5 }
    ]);

    return {
      categories: categoryMatches.slice(0, 5),
      relatedTerms: relatedTerms.map(term => term._id),
      suggestions: [
        ...categoryMatches.map(cat => ({ type: 'category', value: cat })),
        ...relatedTerms.map(term => ({ type: 'term', value: term._id }))
      ]
    };
  } catch (error) {
    return [];
  }
}

// Helper functions
function getDistribution(array) {
  return array.reduce((acc, item) => {
    acc[item] = (acc[item] || 0) + 1;
    return acc;
  }, {});
}

function calculateComplexity(question) {
  let complexity = 1;
  
  // Base complexity on type
  const typeComplexity = {
    'MCQ': 1,
    'True/False': 1,
    'Short Answer': 2,
    'Code': 3
  };
  complexity *= typeComplexity[question.questionType] || 1;
  
  // Adjust for difficulty
  const difficultyMultiplier = {
    'Easy': 1,
    'Medium': 1.5,
    'Hard': 2
  };
  complexity *= difficultyMultiplier[question.difficulty] || 1;
  
  // Adjust for options count (for MCQ)
  if (question.options && question.options.length > 4) {
    complexity *= 1.2;
  }
  
  return Math.round(complexity * 10) / 10;
}

function getDifficultyColor(difficulty) {
  const colors = {
    'Easy': '#10B981',
    'Medium': '#F59E0B', 
    'Hard': '#EF4444'
  };
  return colors[difficulty] || '#6B7280';
}

function getTypeIcon(type) {
  const icons = {
    'MCQ': '📝',
    'Code': '💻',
    'Short Answer': '✍️',
    'True/False': '✅'
  };
  return icons[type] || '❓';
}

function getEstimatedTime(type, difficulty) {
  const baseTime = {
    'MCQ': 2,
    'True/False': 1,
    'Short Answer': 5,
    'Code': 15
  };

  const difficultyMultiplier = {
    'Easy': 1,
    'Medium': 1.5,
    'Hard': 2
  };

  return Math.round((baseTime[type] || 3) * (difficultyMultiplier[difficulty] || 1));
}

function buildPaginationInfo(page, limit, total) {
  const totalPages = Math.ceil(total / limit);
  return {
    current: parseInt(page),
    pages: totalPages,
    total,
    limit,
    hasNext: page < totalPages,
    hasPrev: page > 1
  };
}

function generateCacheKey(queryParams) {
  return Buffer.from(JSON.stringify(queryParams)).toString('base64');
}
