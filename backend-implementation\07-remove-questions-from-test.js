// DELETE /api/tests/:testId/questions - Remove Questions from Test
// Enhanced with validation, impact analysis, and automatic test recalculation

exports.removeQuestionsFromTest = async (req, res) => {
  try {
    const { testId } = req.params;
    const { questionIds } = req.body; // Array of question IDs to remove
    const companyId = req.user.companyId;

    // Validate request body
    if (!questionIds || !Array.isArray(questionIds) || questionIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'questionIds array is required and must not be empty',
        expectedFormat: {
          questionIds: ['questionId1', 'questionId2']
        }
      });
    }

    // Find and validate test
    const test = await Test.findOne({ _id: testId, companyId })
      .populate('questions.questionId', 'questionText questionType category difficulty');

    if (!test) {
      return res.status(404).json({
        success: false,
        error: 'Test not found'
      });
    }

    // Enhanced test modification validation
    const modificationCheck = await validateTestModification(test);
    if (!modificationCheck.allowed) {
      return res.status(400).json({
        success: false,
        error: modificationCheck.reason,
        details: modificationCheck.details,
        suggestions: modificationCheck.suggestions
      });
    }

    // Find questions to remove and validate they exist in test
    const questionsToRemove = [];
    const notFoundQuestions = [];
    
    questionIds.forEach(questionId => {
      const questionInTest = test.questions.find(q => 
        q.questionId._id.toString() === questionId
      );
      
      if (questionInTest) {
        questionsToRemove.push(questionInTest);
      } else {
        notFoundQuestions.push(questionId);
      }
    });

    if (notFoundQuestions.length > 0) {
      return res.status(404).json({
        success: false,
        error: 'Some questions are not found in the test',
        notFoundQuestions,
        availableQuestions: test.questions.map(q => ({
          questionId: q.questionId._id,
          questionText: q.questionId.questionText.substring(0, 100) + '...'
        }))
      });
    }

    // Check if removing questions would leave test empty
    const remainingQuestions = test.questions.filter(q => 
      !questionIds.includes(q.questionId._id.toString())
    );

    if (remainingQuestions.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Cannot remove all questions from test',
        suggestion: 'A test must have at least one question. Consider deleting the test instead.'
      });
    }

    // Analyze impact of removal
    const impactAnalysis = analyzeRemovalImpact(questionsToRemove, test);

    // Start transaction for atomic operation
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Remove questions from test
      test.questions = remainingQuestions;

      // Recalculate test metrics
      const updatedMetrics = calculateTestMetrics(remainingQuestions);
      
      // Update test with new metrics
      Object.assign(test, updatedMetrics, {
        updatedAt: new Date(),
        lastModifiedBy: req.user._id
      });

      // Save the updated test
      await test.save({ session });

      // Log the activity
      await logTestActivity({
        testId,
        userId: req.user._id,
        action: 'questions_removed',
        details: `Removed ${questionsToRemove.length} questions from test`,
        metadata: {
          removedQuestions: questionsToRemove.map(q => ({
            questionId: q.questionId._id,
            questionText: q.questionId.questionText.substring(0, 100),
            points: q.points,
            category: q.questionId.category
          })),
          impactAnalysis,
          newTotalQuestions: remainingQuestions.length,
          newTotalMarks: updatedMetrics.totalMarks
        }
      }, session);

      // Send notifications if test has participants
      if (test.participants && test.participants.length > 0) {
        await sendQuestionRemovedNotifications(test, questionsToRemove, impactAnalysis);
      }

      await session.commitTransaction();

      // Populate the updated test for response
      const populatedTest = await Test.findById(testId)
        .populate('questions.questionId', 'questionText questionType category difficulty points')
        .populate('createdBy', 'name email');

      res.json({
        success: true,
        message: `Successfully removed ${questionsToRemove.length} questions from test`,
        test: populatedTest,
        removedQuestions: questionsToRemove.map(q => ({
          questionId: q.questionId._id,
          questionText: q.questionId.questionText,
          points: q.points
        })),
        impactAnalysis,
        summary: {
          questionsRemoved: questionsToRemove.length,
          totalQuestions: remainingQuestions.length,
          totalMarks: updatedMetrics.totalMarks,
          marksReduced: impactAnalysis.pointsLost,
          avgPointsPerQuestion: remainingQuestions.length > 0 ? 
            Math.round(updatedMetrics.totalMarks / remainingQuestions.length * 100) / 100 : 0
        },
        warnings: modificationCheck.warnings || []
      });

    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }

  } catch (error) {
    console.error('Remove questions from test error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// Validate if test can be modified (same as add questions)
async function validateTestModification(test) {
  const now = new Date();
  const scheduledDate = new Date(test.scheduledDate);
  const hasParticipants = test.participants && test.participants.length > 0;
  
  // Check participant status
  const participantStatuses = test.participants?.map(p => p.status) || [];
  const hasActiveParticipants = participantStatuses.some(status => 
    ['in-progress', 'completed'].includes(status)
  );

  if (hasActiveParticipants) {
    return {
      allowed: false,
      reason: 'Cannot remove questions - some participants have already started or completed the test',
      details: {
        hasActiveParticipants: true,
        participantStatuses: test.participants.map(p => ({
          candidateId: p.candidateId,
          status: p.status
        }))
      },
      suggestions: [
        'Create a new test without these questions',
        'Wait for all participants to complete before modifying'
      ]
    };
  }

  // Check if test has started
  if (now >= scheduledDate && hasParticipants) {
    return {
      allowed: false,
      reason: 'Cannot remove questions - test has already started',
      details: {
        testStarted: true,
        scheduledDate: test.scheduledDate,
        currentTime: now
      },
      suggestions: [
        'Reschedule the test to a future date',
        'Create a new test without these questions'
      ]
    };
  }

  const warnings = [];
  if (hasParticipants) {
    warnings.push('Test has assigned participants. Removing questions will affect their test experience.');
  }

  if (scheduledDate <= new Date(Date.now() + 24 * 60 * 60 * 1000)) {
    warnings.push('Test is scheduled within 24 hours. Consider notifying participants about changes.');
  }

  return {
    allowed: true,
    warnings
  };
}

// Analyze the impact of removing questions
function analyzeRemovalImpact(questionsToRemove, test) {
  const pointsLost = questionsToRemove.reduce((sum, q) => sum + (q.points || 1), 0);
  const currentTotalPoints = test.questions.reduce((sum, q) => sum + (q.points || 1), 0);
  const newTotalPoints = currentTotalPoints - pointsLost;
  
  // Analyze category impact
  const categoriesAffected = {};
  questionsToRemove.forEach(q => {
    const category = q.questionId.category;
    if (!categoriesAffected[category]) {
      categoriesAffected[category] = { removed: 0, remaining: 0 };
    }
    categoriesAffected[category].removed++;
  });

  // Count remaining questions by category
  const remainingQuestions = test.questions.filter(q => 
    !questionsToRemove.some(removed => 
      removed.questionId._id.toString() === q.questionId._id.toString()
    )
  );

  remainingQuestions.forEach(q => {
    const category = q.questionId.category;
    if (categoriesAffected[category]) {
      categoriesAffected[category].remaining++;
    }
  });

  // Analyze difficulty impact
  const difficultiesAffected = {};
  questionsToRemove.forEach(q => {
    const difficulty = q.questionId.difficulty;
    difficultiesAffected[difficulty] = (difficultiesAffected[difficulty] || 0) + 1;
  });

  // Check if passing marks need adjustment
  const currentPassingMarks = test.passingMarks || 0;
  const passingPercentage = currentTotalPoints > 0 ? (currentPassingMarks / currentTotalPoints) * 100 : 0;
  const suggestedPassingMarks = Math.round((passingPercentage / 100) * newTotalPoints);

  return {
    pointsLost,
    currentTotalPoints,
    newTotalPoints,
    percentageReduction: currentTotalPoints > 0 ? 
      Math.round((pointsLost / currentTotalPoints) * 100) : 0,
    categoriesAffected,
    difficultiesAffected,
    passingMarksImpact: {
      current: currentPassingMarks,
      suggested: suggestedPassingMarks,
      needsAdjustment: currentPassingMarks > newTotalPoints
    },
    recommendations: generateRemovalRecommendations(
      questionsToRemove, 
      remainingQuestions, 
      categoriesAffected
    )
  };
}

// Generate recommendations based on removal impact
function generateRemovalRecommendations(removed, remaining, categoriesAffected) {
  const recommendations = [];

  // Check for empty categories
  Object.entries(categoriesAffected).forEach(([category, data]) => {
    if (data.remaining === 0) {
      recommendations.push({
        type: 'category_empty',
        message: `Category "${category}" will have no questions after removal`,
        severity: 'warning'
      });
    } else if (data.remaining < 3) {
      recommendations.push({
        type: 'category_low',
        message: `Category "${category}" will have only ${data.remaining} questions`,
        severity: 'info'
      });
    }
  });

  // Check test balance
  if (remaining.length < 5) {
    recommendations.push({
      type: 'test_too_short',
      message: 'Test will have very few questions, consider adding more',
      severity: 'warning'
    });
  }

  return recommendations;
}

// Calculate updated test metrics (same as add questions)
function calculateTestMetrics(questions) {
  const totalMarks = questions.reduce((sum, q) => sum + (q.points || 1), 0);
  
  // Calculate difficulty distribution
  const difficultyCount = questions.reduce((acc, q) => {
    const difficulty = q.questionId?.difficulty || q.difficulty || 'Medium';
    acc[difficulty] = (acc[difficulty] || 0) + 1;
    return acc;
  }, {});

  // Calculate category distribution
  const categoryCount = questions.reduce((acc, q) => {
    const category = q.questionId?.category || q.category || 'General';
    acc[category] = (acc[category] || 0) + 1;
    return acc;
  }, {});

  // Estimate duration
  const estimatedDuration = questions.reduce((total, q) => {
    const questionType = q.questionId?.questionType || q.questionType || 'MCQ';
    const difficulty = q.questionId?.difficulty || q.difficulty || 'Medium';
    
    const baseTime = {
      'MCQ': 2,
      'True/False': 1,
      'Short Answer': 5,
      'Code': 15
    };
    
    const difficultyMultiplier = {
      'Easy': 1,
      'Medium': 1.5,
      'Hard': 2
    };
    
    const questionTime = (baseTime[questionType] || 3) * 
                        (difficultyMultiplier[difficulty] || 1);
    
    return total + questionTime;
  }, 0);

  return {
    totalMarks,
    questionCount: questions.length,
    estimatedDuration: Math.round(estimatedDuration),
    difficultyDistribution: difficultyCount,
    categoryDistribution: categoryCount
  };
}

// Send notifications about removed questions
async function sendQuestionRemovedNotifications(test, removedQuestions, impactAnalysis) {
  try {
    const participants = await User.find({
      _id: { $in: test.participants.map(p => p.candidateId) }
    });

    const emailPromises = participants.map(participant => {
      return sendEmail({
        to: participant.email,
        subject: `Test Updated: Questions Removed - ${test.testName}`,
        template: 'questions-removed',
        data: {
          participantName: participant.name,
          testName: test.testName,
          questionsRemoved: removedQuestions.length,
          newTotalQuestions: test.questions.length,
          pointsReduced: impactAnalysis.pointsLost,
          newTotalPoints: impactAnalysis.newTotalPoints,
          scheduledDate: test.scheduledDate
        }
      });
    });

    await Promise.all(emailPromises);
  } catch (error) {
    console.error('Failed to send question removed notifications:', error);
  }
}

// Log test activity
async function logTestActivity(activityData, session = null) {
  try {
    const options = session ? { session } : {};
    await TestActivity.create([activityData], options);
  } catch (error) {
    console.error('Failed to log test activity:', error);
  }
}
