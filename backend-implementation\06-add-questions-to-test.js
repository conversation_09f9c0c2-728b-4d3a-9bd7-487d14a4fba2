// POST /api/tests/:testId/questions - Add Questions to Test
// Enhanced with validation, conflict detection, and automatic test updates

exports.addQuestionsToTest = async (req, res) => {
  try {
    const { testId } = req.params;
    const { questions } = req.body; // Array of {questionId, points, optionsSnapshot}
    const companyId = req.user.companyId;

    // Validate request body
    if (!questions || !Array.isArray(questions) || questions.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Questions array is required and must not be empty',
        expectedFormat: {
          questions: [
            {
              questionId: 'string (required)',
              points: 'number (optional, default: 1)',
              optionsSnapshot: 'array (optional)'
            }
          ]
        }
      });
    }

    // Find and validate test
    const test = await Test.findOne({ _id: testId, companyId })
      .populate('questions.questionId', 'questionText questionType category difficulty');

    if (!test) {
      return res.status(404).json({
        success: false,
        error: 'Test not found'
      });
    }

    // Enhanced test modification validation
    const modificationCheck = await validateTestModification(test);
    if (!modificationCheck.allowed) {
      return res.status(400).json({
        success: false,
        error: modificationCheck.reason,
        details: modificationCheck.details,
        suggestions: modificationCheck.suggestions
      });
    }

    // Validate all question IDs exist and belong to company
    const questionIds = questions.map(q => q.questionId);
    const validQuestions = await Question.find({
      _id: { $in: questionIds },
      companyId,
      isActive: true
    }).lean();

    if (validQuestions.length !== questionIds.length) {
      const foundIds = validQuestions.map(q => q._id.toString());
      const invalidIds = questionIds.filter(id => !foundIds.includes(id));
      
      return res.status(400).json({
        success: false,
        error: 'Some questions are invalid or not found',
        invalidQuestionIds: invalidIds,
        validQuestionCount: validQuestions.length,
        requestedQuestionCount: questionIds.length
      });
    }

    // Check for duplicate questions in the request
    const duplicateCheck = checkForDuplicates(questions);
    if (duplicateCheck.hasDuplicates) {
      return res.status(400).json({
        success: false,
        error: 'Duplicate questions found in request',
        duplicates: duplicateCheck.duplicates
      });
    }

    // Check for questions already in test
    const existingQuestionIds = test.questions.map(q => q.questionId.toString());
    const conflictingQuestions = questions.filter(q => 
      existingQuestionIds.includes(q.questionId)
    );

    if (conflictingQuestions.length > 0) {
      return res.status(409).json({
        success: false,
        error: 'Some questions are already in the test',
        conflictingQuestions: conflictingQuestions.map(q => q.questionId),
        suggestion: 'Remove conflicting questions or use update endpoint to modify existing questions'
      });
    }

    // Start transaction for atomic operation
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Prepare questions with enhanced metadata
      const questionsToAdd = await prepareQuestionsForTest(questions, validQuestions);

      // Add questions to test
      test.questions.push(...questionsToAdd);

      // Recalculate test metrics
      const updatedMetrics = calculateTestMetrics(test.questions, validQuestions);
      
      // Update test with new metrics
      Object.assign(test, updatedMetrics, {
        updatedAt: new Date(),
        lastModifiedBy: req.user._id
      });

      // Save the updated test
      await test.save({ session });

      // Log the activity
      await logTestActivity({
        testId,
        userId: req.user._id,
        action: 'questions_added',
        details: `Added ${questions.length} questions to test`,
        metadata: {
          addedQuestions: questionsToAdd.map(q => ({
            questionId: q.questionId,
            points: q.points,
            category: q.category
          })),
          newTotalQuestions: test.questions.length,
          newTotalMarks: updatedMetrics.totalMarks
        }
      }, session);

      // Send notifications if test has participants
      if (test.participants && test.participants.length > 0) {
        await sendQuestionAddedNotifications(test, questionsToAdd);
      }

      await session.commitTransaction();

      // Populate the updated test for response
      const populatedTest = await Test.findById(testId)
        .populate('questions.questionId', 'questionText questionType category difficulty points')
        .populate('createdBy', 'name email');

      res.json({
        success: true,
        message: `Successfully added ${questions.length} questions to test`,
        test: populatedTest,
        addedQuestions: questionsToAdd,
        summary: {
          questionsAdded: questions.length,
          totalQuestions: populatedTest.questions.length,
          totalMarks: updatedMetrics.totalMarks,
          avgPointsPerQuestion: Math.round(updatedMetrics.totalMarks / populatedTest.questions.length * 100) / 100
        },
        warnings: modificationCheck.warnings || []
      });

    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }

  } catch (error) {
    console.error('Add questions to test error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// Validate if test can be modified
async function validateTestModification(test) {
  const now = new Date();
  const scheduledDate = new Date(test.scheduledDate);
  const hasParticipants = test.participants && test.participants.length > 0;
  
  // Check participant status
  const participantStatuses = test.participants?.map(p => p.status) || [];
  const hasActiveParticipants = participantStatuses.some(status => 
    ['in-progress', 'completed'].includes(status)
  );

  if (hasActiveParticipants) {
    return {
      allowed: false,
      reason: 'Cannot add questions - some participants have already started or completed the test',
      details: {
        hasActiveParticipants: true,
        participantStatuses: test.participants.map(p => ({
          candidateId: p.candidateId,
          status: p.status
        }))
      },
      suggestions: [
        'Create a new test for additional questions',
        'Wait for all participants to complete before modifying'
      ]
    };
  }

  // Check if test has started
  if (now >= scheduledDate && hasParticipants) {
    return {
      allowed: false,
      reason: 'Cannot add questions - test has already started',
      details: {
        testStarted: true,
        scheduledDate: test.scheduledDate,
        currentTime: now
      },
      suggestions: [
        'Reschedule the test to a future date',
        'Create a new test with additional questions'
      ]
    };
  }

  const warnings = [];
  if (hasParticipants) {
    warnings.push('Test has assigned participants. Adding questions will affect their test experience.');
  }

  if (scheduledDate <= new Date(Date.now() + 24 * 60 * 60 * 1000)) {
    warnings.push('Test is scheduled within 24 hours. Consider notifying participants about changes.');
  }

  return {
    allowed: true,
    warnings
  };
}

// Check for duplicate questions in request
function checkForDuplicates(questions) {
  const questionIds = questions.map(q => q.questionId);
  const uniqueIds = [...new Set(questionIds)];
  
  if (questionIds.length !== uniqueIds.length) {
    const duplicates = questionIds.filter((id, index) => 
      questionIds.indexOf(id) !== index
    );
    
    return {
      hasDuplicates: true,
      duplicates: [...new Set(duplicates)]
    };
  }
  
  return { hasDuplicates: false };
}

// Prepare questions with enhanced metadata
async function prepareQuestionsForTest(requestQuestions, validQuestions) {
  return requestQuestions.map(reqQuestion => {
    const questionData = validQuestions.find(q => 
      q._id.toString() === reqQuestion.questionId
    );

    return {
      questionId: reqQuestion.questionId,
      points: reqQuestion.points || questionData.points || 1,
      optionsSnapshot: reqQuestion.optionsSnapshot || questionData.options || [],
      addedAt: new Date(),
      category: questionData.category,
      difficulty: questionData.difficulty,
      questionType: questionData.questionType
    };
  });
}

// Calculate updated test metrics
function calculateTestMetrics(allQuestions, newQuestionData) {
  const totalMarks = allQuestions.reduce((sum, q) => sum + (q.points || 1), 0);
  
  // Calculate difficulty distribution
  const difficultyCount = allQuestions.reduce((acc, q) => {
    const difficulty = q.difficulty || 'Medium';
    acc[difficulty] = (acc[difficulty] || 0) + 1;
    return acc;
  }, {});

  // Calculate category distribution
  const categoryCount = allQuestions.reduce((acc, q) => {
    const category = q.category || 'General';
    acc[category] = (acc[category] || 0) + 1;
    return acc;
  }, {});

  // Estimate duration based on question types and difficulties
  const estimatedDuration = allQuestions.reduce((total, q) => {
    const baseTime = {
      'MCQ': 2,
      'True/False': 1,
      'Short Answer': 5,
      'Code': 15
    };
    
    const difficultyMultiplier = {
      'Easy': 1,
      'Medium': 1.5,
      'Hard': 2
    };
    
    const questionTime = (baseTime[q.questionType] || 3) * 
                        (difficultyMultiplier[q.difficulty] || 1);
    
    return total + questionTime;
  }, 0);

  return {
    totalMarks,
    questionCount: allQuestions.length,
    estimatedDuration: Math.round(estimatedDuration),
    difficultyDistribution: difficultyCount,
    categoryDistribution: categoryCount
  };
}

// Send notifications about added questions
async function sendQuestionAddedNotifications(test, addedQuestions) {
  try {
    // This would integrate with your notification system
    const participants = await User.find({
      _id: { $in: test.participants.map(p => p.candidateId) }
    });

    const emailPromises = participants.map(participant => {
      return sendEmail({
        to: participant.email,
        subject: `Test Updated: New Questions Added - ${test.testName}`,
        template: 'questions-added',
        data: {
          participantName: participant.name,
          testName: test.testName,
          questionsAdded: addedQuestions.length,
          newTotalQuestions: test.questions.length,
          scheduledDate: test.scheduledDate
        }
      });
    });

    await Promise.all(emailPromises);
  } catch (error) {
    console.error('Failed to send question added notifications:', error);
  }
}

// Log test activity
async function logTestActivity(activityData, session = null) {
  try {
    const options = session ? { session } : {};
    await TestActivity.create([activityData], options);
  } catch (error) {
    console.error('Failed to log test activity:', error);
  }
}
