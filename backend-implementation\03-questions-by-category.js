// GET /api/tests/questions/by-category?category=Frontend
// Enhanced with pagination, filtering, statistics, and usage analytics

exports.getQuestionsByCategory = async (req, res) => {
  try {
    const { 
      category, 
      page = 1, 
      limit = 20, 
      difficulty, 
      type, 
      sortBy = 'createdAt', 
      sortOrder = 'desc',
      includeStats = false,
      includeUsage = false,
      excludeUsedInTest = null
    } = req.query;
    
    const companyId = req.user.companyId;

    if (!category) {
      return res.status(400).json({
        success: false,
        error: 'Category parameter is required',
        availableCategories: await getAvailableCategories(companyId)
      });
    }

    // Build comprehensive filter
    const filter = await buildQuestionFilter({
      companyId,
      category,
      difficulty,
      type,
      excludeUsedInTest
    });

    // Pagination setup
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const limitNum = parseInt(limit);

    // Build sort object with enhanced options
    const sortObj = buildSortObject(sortBy, sortOrder);

    // Execute parallel queries for performance
    const [questions, totalCount, categoryStats] = await Promise.all([
      Question.find(filter)
        .populate('createdBy', 'name email')
        .sort(sortObj)
        .skip(skip)
        .limit(limitNum)
        .lean(),
      Question.countDocuments(filter),
      includeStats === 'true' ? getCategoryStatistics(companyId, category) : null
    ]);

    // Enhanced question data with usage analytics
    const enhancedQuestions = await enhanceQuestionsWithMetadata(
      questions, 
      includeUsage === 'true'
    );

    // Build response with comprehensive pagination info
    const pagination = buildPaginationInfo(page, limitNum, totalCount);

    // Get filter suggestions
    const filterSuggestions = await getFilterSuggestions(companyId, category);

    res.json({
      success: true,
      questions: enhancedQuestions,
      pagination,
      category: {
        name: category,
        questionCount: totalCount,
        stats: categoryStats
      },
      filters: {
        applied: { category, difficulty, type },
        suggestions: filterSuggestions
      },
      metadata: {
        responseTime: Date.now() - req.startTime,
        cacheStatus: 'fresh', // Can be enhanced with Redis caching
        lastUpdated: new Date()
      }
    });

  } catch (error) {
    console.error('Get questions by category error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// Enhanced filter building
async function buildQuestionFilter({ companyId, category, difficulty, type, excludeUsedInTest }) {
  const filter = {
    companyId: new mongoose.Types.ObjectId(companyId),
    category: { $regex: new RegExp(`^${category}$`, 'i') },
    isActive: true
  };

  // Add optional filters
  if (difficulty) {
    filter.difficulty = difficulty;
  }

  if (type) {
    filter.questionType = type;
  }

  // Exclude questions already used in a specific test
  if (excludeUsedInTest) {
    const usedQuestions = await Test.findById(excludeUsedInTest)
      .select('questions.questionId')
      .lean();
    
    if (usedQuestions) {
      const usedQuestionIds = usedQuestions.questions.map(q => q.questionId);
      filter._id = { $nin: usedQuestionIds };
    }
  }

  return filter;
}

// Enhanced sort options
function buildSortObject(sortBy, sortOrder) {
  const validSortFields = {
    'createdAt': 'createdAt',
    'difficulty': 'difficulty',
    'type': 'questionType',
    'points': 'points',
    'usage': 'usageCount', // Virtual field
    'name': 'questionText'
  };

  const sortField = validSortFields[sortBy] || 'createdAt';
  const order = sortOrder === 'asc' ? 1 : -1;

  return { [sortField]: order };
}

// Enhanced question metadata
async function enhanceQuestionsWithMetadata(questions, includeUsage) {
  return await Promise.all(
    questions.map(async (question) => {
      const enhanced = {
        ...question,
        difficultyColor: getDifficultyColor(question.difficulty),
        typeIcon: getTypeIcon(question.questionType),
        estimatedTime: getEstimatedTime(question.questionType, question.difficulty)
      };

      if (includeUsage) {
        enhanced.usageStats = await getQuestionUsageStats(question._id);
      }

      return enhanced;
    })
  );
}

// Get question usage statistics
async function getQuestionUsageStats(questionId) {
  try {
    const usageStats = await Test.aggregate([
      { $unwind: '$questions' },
      { $match: { 'questions.questionId': questionId } },
      {
        $group: {
          _id: null,
          usedInTests: { $sum: 1 },
          totalAttempts: { $sum: { $size: '$participants' } },
          avgScore: { $avg: '$questions.points' }
        }
      }
    ]);

    // Get recent usage
    const recentUsage = await Test.find(
      { 'questions.questionId': questionId },
      { testName: 1, createdAt: 1 }
    )
    .sort({ createdAt: -1 })
    .limit(3)
    .lean();

    return {
      ...usageStats[0] || { usedInTests: 0, totalAttempts: 0, avgScore: 0 },
      recentTests: recentUsage,
      lastUsed: recentUsage[0]?.createdAt || null
    };
  } catch (error) {
    console.error('Error getting usage stats:', error);
    return { usedInTests: 0, totalAttempts: 0, avgScore: 0 };
  }
}

// Get comprehensive category statistics
async function getCategoryStatistics(companyId, category) {
  try {
    const stats = await Question.aggregate([
      { 
        $match: { 
          companyId: new mongoose.Types.ObjectId(companyId), 
          category,
          isActive: true
        } 
      },
      {
        $group: {
          _id: null,
          totalQuestions: { $sum: 1 },
          difficulties: { $push: '$difficulty' },
          types: { $push: '$questionType' },
          avgPoints: { $avg: '$points' },
          totalPoints: { $sum: '$points' },
          createdThisMonth: {
            $sum: {
              $cond: [
                { $gte: ['$createdAt', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)] },
                1,
                0
              ]
            }
          }
        }
      },
      {
        $project: {
          totalQuestions: 1,
          avgPoints: { $round: ['$avgPoints', 2] },
          totalPoints: 1,
          createdThisMonth: 1,
          difficultyBreakdown: {
            $arrayToObject: {
              $map: {
                input: [
                  { k: 'Easy', v: { $size: { $filter: { input: '$difficulties', cond: { $eq: ['$$this', 'Easy'] } } } } },
                  { k: 'Medium', v: { $size: { $filter: { input: '$difficulties', cond: { $eq: ['$$this', 'Medium'] } } } } },
                  { k: 'Hard', v: { $size: { $filter: { input: '$difficulties', cond: { $eq: ['$$this', 'Hard'] } } } } }
                ],
                in: { k: '$$this.k', v: '$$this.v' }
              }
            }
          },
          typeBreakdown: {
            $arrayToObject: {
              $map: {
                input: [
                  { k: 'MCQ', v: { $size: { $filter: { input: '$types', cond: { $eq: ['$$this', 'MCQ'] } } } } },
                  { k: 'Code', v: { $size: { $filter: { input: '$types', cond: { $eq: ['$$this', 'Code'] } } } } },
                  { k: 'Short Answer', v: { $size: { $filter: { input: '$types', cond: { $eq: ['$$this', 'Short Answer'] } } } } },
                  { k: 'True/False', v: { $size: { $filter: { input: '$types', cond: { $eq: ['$$this', 'True/False'] } } } } }
                ],
                in: { k: '$$this.k', v: '$$this.v' }
              }
            }
          }
        }
      }
    ]);

    return stats[0] || null;
  } catch (error) {
    console.error('Error getting category statistics:', error);
    return null;
  }
}

// Get available categories for the company
async function getAvailableCategories(companyId) {
  try {
    return await Question.distinct('category', {
      companyId: new mongoose.Types.ObjectId(companyId),
      isActive: true
    });
  } catch (error) {
    return [];
  }
}

// Get filter suggestions
async function getFilterSuggestions(companyId, category) {
  try {
    const [difficulties, types] = await Promise.all([
      Question.distinct('difficulty', {
        companyId: new mongoose.Types.ObjectId(companyId),
        category,
        isActive: true
      }),
      Question.distinct('questionType', {
        companyId: new mongoose.Types.ObjectId(companyId),
        category,
        isActive: true
      })
    ]);

    return {
      difficulties: difficulties.sort(),
      types: types.sort(),
      sortOptions: [
        { value: 'createdAt', label: 'Date Created' },
        { value: 'difficulty', label: 'Difficulty' },
        { value: 'type', label: 'Question Type' },
        { value: 'points', label: 'Points' },
        { value: 'usage', label: 'Usage Count' }
      ]
    };
  } catch (error) {
    return { difficulties: [], types: [], sortOptions: [] };
  }
}

// Helper functions
function getDifficultyColor(difficulty) {
  const colors = {
    'Easy': '#10B981',
    'Medium': '#F59E0B',
    'Hard': '#EF4444'
  };
  return colors[difficulty] || '#6B7280';
}

function getTypeIcon(type) {
  const icons = {
    'MCQ': '📝',
    'Code': '💻',
    'Short Answer': '✍️',
    'True/False': '✅'
  };
  return icons[type] || '❓';
}

function getEstimatedTime(type, difficulty) {
  const baseTime = {
    'MCQ': 2,
    'True/False': 1,
    'Short Answer': 5,
    'Code': 15
  };

  const difficultyMultiplier = {
    'Easy': 1,
    'Medium': 1.5,
    'Hard': 2
  };

  return Math.round((baseTime[type] || 3) * (difficultyMultiplier[difficulty] || 1));
}

function buildPaginationInfo(page, limit, total) {
  const totalPages = Math.ceil(total / limit);
  return {
    current: parseInt(page),
    pages: totalPages,
    total,
    limit,
    hasNext: page < totalPages,
    hasPrev: page > 1,
    nextPage: page < totalPages ? parseInt(page) + 1 : null,
    prevPage: page > 1 ? parseInt(page) - 1 : null
  };
}
