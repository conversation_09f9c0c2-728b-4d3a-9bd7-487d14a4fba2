// DELETE /api/tests/:testId - Delete Test
// Enhanced with safety checks, cascade deletion, and audit trail

exports.deleteTest = async (req, res) => {
  try {
    const { testId } = req.params;
    const companyId = req.user.companyId;
    const { force = false, archive = false } = req.query;

    // Find the test with full details
    const test = await Test.findOne({ _id: testId, companyId })
      .populate('participants.candidateId', 'name email')
      .populate('questions.questionId', 'questionText');

    if (!test) {
      return res.status(404).json({
        success: false,
        error: 'Test not found'
      });
    }

    // If archive is requested, perform soft delete
    if (archive === 'true') {
      return await archiveTest(req, res, test);
    }

    // Enhanced safety checks
    const safetyCheck = await performDeletionSafetyCheck(test);
    if (!safetyCheck.canDelete && !force) {
      return res.status(400).json({
        success: false,
        error: 'Cannot delete test due to safety restrictions',
        details: safetyCheck.restrictions,
        suggestions: safetyCheck.suggestions
      });
    }

    // Check for ongoing submissions
    const ongoingSubmissions = await TestSubmission.countDocuments({
      testId,
      status: 'in_progress'
    });

    if (ongoingSubmissions > 0 && !force) {
      return res.status(400).json({
        success: false,
        error: `Cannot delete test with ${ongoingSubmissions} ongoing submissions`,
        suggestion: 'Wait for submissions to complete or use ?force=true'
      });
    }

    // Start transaction for safe cascade deletion
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Store test data for audit trail
      const testAuditData = {
        testId: test._id,
        testName: test.testName,
        companyId: test.companyId,
        participantCount: test.participants?.length || 0,
        questionCount: test.questions?.length || 0,
        status: test.status,
        createdAt: test.createdAt,
        scheduledDate: test.scheduledDate,
        deletedBy: req.user._id,
        deletedAt: new Date(),
        forceDeleted: force === 'true'
      };

      // Cascade deletions with detailed tracking
      const deletionResults = await performCascadeDeletion(testId, session);

      // Create audit trail before deleting the test
      await TestDeletionAudit.create([testAuditData], { session });

      // Delete the test itself
      await Test.findByIdAndDelete(testId, { session });

      // Send notifications to participants about test cancellation
      if (test.participants?.length > 0) {
        await sendTestDeletionNotifications(test);
      }

      await session.commitTransaction();

      res.json({
        success: true,
        message: 'Test and all related data deleted successfully',
        deletedData: {
          test: {
            id: testId,
            name: test.testName,
            participantCount: test.participants?.length || 0,
            questionCount: test.questions?.length || 0
          },
          cascadeDeleted: deletionResults
        },
        auditTrail: {
          auditId: testAuditData.testId,
          deletedAt: testAuditData.deletedAt
        }
      });

    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }

  } catch (error) {
    console.error('Delete test error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// Enhanced safety check function
async function performDeletionSafetyCheck(test) {
  const restrictions = [];
  const suggestions = [];
  
  const hasParticipants = test.participants && test.participants.length > 0;
  const hasCompletedSubmissions = test.participants?.some(p => p.status === 'completed');
  const isScheduledSoon = test.scheduledDate && 
    new Date(test.scheduledDate) <= new Date(Date.now() + 24 * 60 * 60 * 1000); // Within 24 hours

  if (hasCompletedSubmissions) {
    restrictions.push({
      type: 'completed_submissions',
      message: 'Test has completed submissions',
      count: test.participants.filter(p => p.status === 'completed').length
    });
    suggestions.push('Consider archiving instead of deleting to preserve submission data');
  }

  if (hasParticipants && !hasCompletedSubmissions) {
    restrictions.push({
      type: 'assigned_participants',
      message: 'Test has assigned participants',
      count: test.participants.length
    });
    suggestions.push('Notify participants before deletion or use ?archive=true');
  }

  if (isScheduledSoon) {
    restrictions.push({
      type: 'scheduled_soon',
      message: 'Test is scheduled within 24 hours',
      scheduledDate: test.scheduledDate
    });
    suggestions.push('Consider rescheduling instead of deleting');
  }

  return {
    canDelete: restrictions.length === 0,
    restrictions,
    suggestions
  };
}

// Cascade deletion function
async function performCascadeDeletion(testId, session) {
  const deletionResults = {};

  try {
    // Delete test submissions
    const submissionResult = await TestSubmission.deleteMany({ testId }, { session });
    deletionResults.submissions = submissionResult.deletedCount;

    // Delete test results
    const resultResult = await TestResult.deleteMany({ testId }, { session });
    deletionResults.results = resultResult.deletedCount;

    // Delete test analytics
    const analyticsResult = await TestAnalytics.deleteMany({ testId }, { session });
    deletionResults.analytics = analyticsResult.deletedCount;

    // Delete notifications
    const notificationResult = await Notification.deleteMany({ 
      'metadata.testId': testId 
    }, { session });
    deletionResults.notifications = notificationResult.deletedCount;

    // Update question bundles (remove test reference)
    const bundleResult = await QuestionBundle.updateMany(
      { 'usedInTests': testId },
      { $pull: { usedInTests: testId } },
      { session }
    );
    deletionResults.bundleUpdates = bundleResult.modifiedCount;

    return deletionResults;
  } catch (error) {
    console.error('Cascade deletion error:', error);
    throw error;
  }
}

// Archive test function (soft delete)
async function archiveTest(req, res, test) {
  try {
    const archivedTest = await Test.findByIdAndUpdate(
      test._id,
      { 
        isActive: false,
        status: 'archived',
        archivedAt: new Date(),
        archivedBy: req.user._id,
        archiveReason: req.body.reason || 'Manual archive'
      },
      { new: true }
    );

    // Log archival activity
    await logTestActivity({
      testId: test._id,
      userId: req.user._id,
      action: 'test_archived',
      details: `Test "${test.testName}" archived`,
      metadata: { reason: req.body.reason }
    });

    res.json({
      success: true,
      message: 'Test archived successfully',
      test: archivedTest,
      note: 'Test data preserved. Use restore endpoint to reactivate.'
    });

  } catch (error) {
    throw error;
  }
}

// Send deletion notifications
async function sendTestDeletionNotifications(test) {
  try {
    const participants = test.participants.map(p => p.candidateId);
    
    // Send email notifications
    const emailPromises = participants.map(participant => {
      return sendEmail({
        to: participant.email,
        subject: `Test Cancelled: ${test.testName}`,
        template: 'test-cancelled',
        data: {
          participantName: participant.name,
          testName: test.testName,
          scheduledDate: test.scheduledDate,
          reason: 'The test has been cancelled by the administrator'
        }
      });
    });

    await Promise.all(emailPromises);
  } catch (error) {
    console.error('Failed to send deletion notifications:', error);
  }
}

// Restore archived test
exports.restoreTest = async (req, res) => {
  try {
    const { testId } = req.params;
    const companyId = req.user.companyId;

    const test = await Test.findOneAndUpdate(
      { _id: testId, companyId, status: 'archived' },
      { 
        isActive: true,
        status: 'draft',
        $unset: { archivedAt: 1, archivedBy: 1, archiveReason: 1 }
      },
      { new: true }
    );

    if (!test) {
      return res.status(404).json({
        success: false,
        error: 'Archived test not found'
      });
    }

    // Log restoration activity
    await logTestActivity({
      testId,
      userId: req.user._id,
      action: 'test_restored',
      details: `Test "${test.testName}" restored from archive`
    });

    res.json({
      success: true,
      message: 'Test restored successfully',
      test
    });

  } catch (error) {
    console.error('Restore test error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};
