// PUT /api/tests/:testId - Update Test
// Enhanced with validation, status management, and activity logging

exports.updateTest = async (req, res) => {
  try {
    const { testId } = req.params;
    const companyId = req.user.companyId;
    const updates = req.body;

    // Find the test
    const test = await Test.findOne({ _id: testId, companyId });
    if (!test) {
      return res.status(404).json({
        success: false,
        error: 'Test not found'
      });
    }

    // Enhanced status validation
    const canModify = await checkTestModificationPermission(test);
    if (!canModify.allowed) {
      return res.status(400).json({
        success: false,
        error: canModify.reason,
        details: canModify.details
      });
    }

    // Validate update fields
    const allowedUpdates = [
      'testName', 'description', 'duration', 'totalMarks', 'passingMarks',
      'instructions', 'scheduledDate', 'endDate', 'timeLimit', 'shuffleQuestions',
      'showResults', 'allowReview', 'proctoring', 'isActive', 'difficulty'
    ];

    const updateFields = {};
    Object.keys(updates).forEach(key => {
      if (allowedUpdates.includes(key)) {
        updateFields[key] = updates[key];
      }
    });

    // Enhanced validations
    if (updateFields.scheduledDate && updateFields.endDate) {
      if (new Date(updateFields.scheduledDate) >= new Date(updateFields.endDate)) {
        return res.status(400).json({
          success: false,
          error: 'Scheduled date must be before end date'
        });
      }
    }

    if (updateFields.passingMarks && (updateFields.totalMarks || test.totalMarks)) {
      const totalMarks = updateFields.totalMarks || test.totalMarks;
      if (updateFields.passingMarks > totalMarks) {
        return res.status(400).json({
          success: false,
          error: 'Passing marks cannot exceed total marks'
        });
      }
    }

    // Calculate total marks if questions are present
    if (test.questions && test.questions.length > 0) {
      const calculatedTotalMarks = test.questions.reduce((sum, q) => sum + (q.points || 1), 0);
      updateFields.totalMarks = calculatedTotalMarks;
    }

    // Update test with enhanced tracking
    const updatedTest = await Test.findByIdAndUpdate(
      testId,
      { 
        ...updateFields,
        updatedAt: new Date(),
        lastModifiedBy: req.user._id
      },
      { new: true, runValidators: true }
    ).populate([
      { 
        path: 'questions.questionId', 
        select: 'questionText questionType category difficulty points' 
      },
      { path: 'createdBy', select: 'name email' },
      { 
        path: 'participants.candidateId', 
        select: 'name email phone' 
      }
    ]);

    // Log the update activity
    await logTestActivity({
      testId,
      userId: req.user._id,
      action: 'test_updated',
      details: `Test updated: ${Object.keys(updateFields).join(', ')}`,
      metadata: {
        updatedFields: updateFields,
        previousValues: Object.keys(updateFields).reduce((prev, key) => {
          prev[key] = test[key];
          return prev;
        }, {})
      }
    });

    // Send notifications if test is scheduled and participants are assigned
    if (updateFields.scheduledDate && test.participants?.length > 0) {
      await sendTestUpdateNotifications(test, updateFields);
    }

    res.json({
      success: true,
      message: 'Test updated successfully',
      test: updatedTest,
      updatedFields: Object.keys(updateFields),
      warnings: canModify.warnings || []
    });

  } catch (error) {
    console.error('Update test error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// Helper function to check modification permissions
async function checkTestModificationPermission(test) {
  const now = new Date();
  const scheduledDate = new Date(test.scheduledDate);
  const hasParticipants = test.participants && test.participants.length > 0;
  
  // Check if any participant has started the test
  const hasStartedParticipants = test.participants?.some(p => 
    p.status === 'in-progress' || p.status === 'completed'
  );

  if (hasStartedParticipants) {
    return {
      allowed: false,
      reason: 'Cannot modify test - some candidates have already started or completed the test',
      details: {
        hasStartedParticipants: true,
        participantStatuses: test.participants.map(p => ({
          candidateId: p.candidateId,
          status: p.status
        }))
      }
    };
  }

  if (now >= scheduledDate && hasParticipants) {
    return {
      allowed: false,
      reason: 'Cannot modify test - test has already started and has participants',
      details: {
        testStarted: true,
        hasParticipants: true
      }
    };
  }

  const warnings = [];
  if (hasParticipants) {
    warnings.push('Test has assigned candidates. Changes will affect their test experience.');
  }

  return {
    allowed: true,
    warnings
  };
}

// Helper function to log test activities
async function logTestActivity({ testId, userId, action, details, metadata = {} }) {
  try {
    // Assuming you have a TestActivity model
    await TestActivity.create({
      testId,
      userId,
      action,
      details,
      metadata,
      timestamp: new Date()
    });
  } catch (error) {
    console.error('Failed to log test activity:', error);
  }
}

// Helper function to send notifications
async function sendTestUpdateNotifications(test, updates) {
  try {
    // Send email notifications to participants about test updates
    const participants = await User.find({
      _id: { $in: test.participants.map(p => p.candidateId) }
    });

    const emailPromises = participants.map(participant => {
      return sendEmail({
        to: participant.email,
        subject: `Test Updated: ${test.testName}`,
        template: 'test-updated',
        data: {
          participantName: participant.name,
          testName: test.testName,
          updates: Object.keys(updates),
          scheduledDate: test.scheduledDate,
          duration: test.duration
        }
      });
    });

    await Promise.all(emailPromises);
  } catch (error) {
    console.error('Failed to send update notifications:', error);
  }
}

// Validation middleware
exports.validateTestUpdate = (req, res, next) => {
  const { body } = req;
  
  // Validate duration
  if (body.duration && (body.duration < 1 || body.duration > 480)) {
    return res.status(400).json({
      success: false,
      error: 'Duration must be between 1 and 480 minutes'
    });
  }

  // Validate marks
  if (body.totalMarks && body.totalMarks < 1) {
    return res.status(400).json({
      success: false,
      error: 'Total marks must be at least 1'
    });
  }

  // Validate test name
  if (body.testName && body.testName.trim().length < 3) {
    return res.status(400).json({
      success: false,
      error: 'Test name must be at least 3 characters long'
    });
  }

  next();
};
