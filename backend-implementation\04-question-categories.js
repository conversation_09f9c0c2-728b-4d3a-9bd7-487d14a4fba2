// GET /api/tests/questions/categories
// Enhanced with statistics, trending categories, and usage analytics

exports.getQuestionCategories = async (req, res) => {
  try {
    const companyId = req.user.companyId;
    const { 
      includeStats = false, 
      includeTrending = false,
      sortBy = 'name',
      sortOrder = 'asc',
      minQuestions = 0
    } = req.query;

    // Get all categories with question counts
    const categoriesWithCounts = await Question.aggregate([
      {
        $match: {
          companyId: new mongoose.Types.ObjectId(companyId),
          isActive: true
        }
      },
      {
        $group: {
          _id: '$category',
          questionCount: { $sum: 1 },
          difficulties: { $addToSet: '$difficulty' },
          types: { $addToSet: '$questionType' },
          avgPoints: { $avg: '$points' },
          totalPoints: { $sum: '$points' },
          lastUpdated: { $max: '$updatedAt' },
          createdThisWeek: {
            $sum: {
              $cond: [
                { $gte: ['$createdAt', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)] },
                1,
                0
              ]
            }
          }
        }
      },
      {
        $match: {
          questionCount: { $gte: parseInt(minQuestions) }
        }
      },
      {
        $project: {
          category: '$_id',
          questionCount: 1,
          difficulties: 1,
          types: 1,
          avgPoints: { $round: ['$avgPoints', 2] },
          totalPoints: 1,
          lastUpdated: 1,
          createdThisWeek: 1,
          difficultyCount: { $size: '$difficulties' },
          typeCount: { $size: '$types' }
        }
      }
    ]);

    // Filter out null/empty categories
    const validCategories = categoriesWithCounts.filter(cat => 
      cat.category && cat.category.trim() !== ''
    );

    // Enhanced categories with additional metadata
    let enhancedCategories = await Promise.all(
      validCategories.map(async (category) => {
        const enhanced = {
          ...category,
          icon: getCategoryIcon(category.category),
          color: getCategoryColor(category.category),
          description: getCategoryDescription(category.category)
        };

        // Add detailed statistics if requested
        if (includeStats === 'true') {
          enhanced.detailedStats = await getCategoryDetailedStats(companyId, category.category);
        }

        // Add usage analytics if requested
        if (includeTrending === 'true') {
          enhanced.trendingData = await getCategoryTrendingData(companyId, category.category);
        }

        return enhanced;
      })
    );

    // Sort categories
    enhancedCategories = sortCategories(enhancedCategories, sortBy, sortOrder);

    // Get trending categories if requested
    let trendingCategories = null;
    if (includeTrending === 'true') {
      trendingCategories = await getTrendingCategories(companyId);
    }

    // Get category recommendations
    const recommendations = await getCategoryRecommendations(companyId, enhancedCategories);

    res.json({
      success: true,
      categories: enhancedCategories,
      summary: {
        totalCategories: enhancedCategories.length,
        totalQuestions: enhancedCategories.reduce((sum, cat) => sum + cat.questionCount, 0),
        avgQuestionsPerCategory: Math.round(
          enhancedCategories.reduce((sum, cat) => sum + cat.questionCount, 0) / enhancedCategories.length
        ),
        mostPopularCategory: enhancedCategories.sort((a, b) => b.questionCount - a.questionCount)[0]?.category,
        recentlyUpdated: enhancedCategories.filter(cat => 
          new Date(cat.lastUpdated) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        ).length
      },
      trending: trendingCategories,
      recommendations,
      metadata: {
        generatedAt: new Date(),
        filters: { minQuestions, sortBy, sortOrder },
        cacheExpiry: new Date(Date.now() + 5 * 60 * 1000) // 5 minutes
      }
    });

  } catch (error) {
    console.error('Get question categories error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// Get detailed statistics for a category
async function getCategoryDetailedStats(companyId, category) {
  try {
    const stats = await Question.aggregate([
      {
        $match: {
          companyId: new mongoose.Types.ObjectId(companyId),
          category,
          isActive: true
        }
      },
      {
        $group: {
          _id: null,
          difficultyBreakdown: {
            $push: {
              difficulty: '$difficulty',
              points: '$points'
            }
          },
          typeBreakdown: {
            $push: {
              type: '$questionType',
              points: '$points'
            }
          },
          pointsDistribution: { $push: '$points' },
          creationTimeline: {
            $push: {
              date: { $dateToString: { format: '%Y-%m', date: '$createdAt' } },
              count: 1
            }
          }
        }
      }
    ]);

    if (!stats[0]) return null;

    const data = stats[0];
    
    // Process difficulty breakdown
    const difficultyStats = data.difficultyBreakdown.reduce((acc, item) => {
      acc[item.difficulty] = (acc[item.difficulty] || 0) + 1;
      return acc;
    }, {});

    // Process type breakdown
    const typeStats = data.typeBreakdown.reduce((acc, item) => {
      acc[item.type] = (acc[item.type] || 0) + 1;
      return acc;
    }, {});

    // Process creation timeline
    const timelineStats = data.creationTimeline.reduce((acc, item) => {
      acc[item.date] = (acc[item.date] || 0) + 1;
      return acc;
    }, {});

    return {
      difficultyBreakdown: difficultyStats,
      typeBreakdown: typeStats,
      pointsDistribution: {
        min: Math.min(...data.pointsDistribution),
        max: Math.max(...data.pointsDistribution),
        avg: Math.round(data.pointsDistribution.reduce((a, b) => a + b, 0) / data.pointsDistribution.length * 100) / 100
      },
      creationTimeline: timelineStats
    };
  } catch (error) {
    console.error('Error getting detailed stats:', error);
    return null;
  }
}

// Get trending data for a category
async function getCategoryTrendingData(companyId, category) {
  try {
    // Get usage in tests over time
    const usageData = await Test.aggregate([
      { $unwind: '$questions' },
      {
        $lookup: {
          from: 'questions',
          localField: 'questions.questionId',
          foreignField: '_id',
          as: 'questionData'
        }
      },
      { $unwind: '$questionData' },
      {
        $match: {
          companyId: new mongoose.Types.ObjectId(companyId),
          'questionData.category': category
        }
      },
      {
        $group: {
          _id: {
            month: { $dateToString: { format: '%Y-%m', date: '$createdAt' } }
          },
          testsCount: { $sum: 1 },
          questionsUsed: { $sum: 1 }
        }
      },
      { $sort: { '_id.month': -1 } },
      { $limit: 6 }
    ]);

    // Calculate trend direction
    const recentUsage = usageData.slice(0, 3).reduce((sum, item) => sum + item.questionsUsed, 0);
    const olderUsage = usageData.slice(3, 6).reduce((sum, item) => sum + item.questionsUsed, 0);
    
    const trendDirection = recentUsage > olderUsage ? 'up' : 
                          recentUsage < olderUsage ? 'down' : 'stable';

    return {
      usageTimeline: usageData.reverse(),
      trendDirection,
      trendPercentage: olderUsage > 0 ? Math.round(((recentUsage - olderUsage) / olderUsage) * 100) : 0
    };
  } catch (error) {
    console.error('Error getting trending data:', error);
    return null;
  }
}

// Get overall trending categories
async function getTrendingCategories(companyId) {
  try {
    const trendingData = await Question.aggregate([
      {
        $match: {
          companyId: new mongoose.Types.ObjectId(companyId),
          isActive: true,
          createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } // Last 30 days
        }
      },
      {
        $group: {
          _id: '$category',
          recentQuestions: { $sum: 1 },
          avgPoints: { $avg: '$points' }
        }
      },
      { $sort: { recentQuestions: -1 } },
      { $limit: 5 }
    ]);

    return trendingData.map(item => ({
      category: item._id,
      recentQuestions: item.recentQuestions,
      avgPoints: Math.round(item.avgPoints * 100) / 100,
      icon: getCategoryIcon(item._id)
    }));
  } catch (error) {
    console.error('Error getting trending categories:', error);
    return [];
  }
}

// Get category recommendations
async function getCategoryRecommendations(companyId, existingCategories) {
  try {
    const lowQuestionCategories = existingCategories
      .filter(cat => cat.questionCount < 5)
      .map(cat => cat.category);

    const suggestions = [];

    if (lowQuestionCategories.length > 0) {
      suggestions.push({
        type: 'expand_categories',
        message: `Consider adding more questions to: ${lowQuestionCategories.slice(0, 3).join(', ')}`,
        categories: lowQuestionCategories
      });
    }

    // Suggest popular categories that are missing
    const popularCategories = [
      'JavaScript', 'Python', 'React', 'Node.js', 'Database', 
      'System Design', 'Data Structures', 'Algorithms'
    ];
    
    const existingCategoryNames = existingCategories.map(cat => cat.category.toLowerCase());
    const missingPopular = popularCategories.filter(cat => 
      !existingCategoryNames.includes(cat.toLowerCase())
    );

    if (missingPopular.length > 0) {
      suggestions.push({
        type: 'add_popular_categories',
        message: `Consider adding questions for popular categories: ${missingPopular.slice(0, 3).join(', ')}`,
        categories: missingPopular
      });
    }

    return suggestions;
  } catch (error) {
    console.error('Error getting recommendations:', error);
    return [];
  }
}

// Sort categories based on criteria
function sortCategories(categories, sortBy, sortOrder) {
  const sortFunctions = {
    name: (a, b) => a.category.localeCompare(b.category),
    questionCount: (a, b) => b.questionCount - a.questionCount,
    lastUpdated: (a, b) => new Date(b.lastUpdated) - new Date(a.lastUpdated),
    avgPoints: (a, b) => b.avgPoints - a.avgPoints
  };

  const sortFn = sortFunctions[sortBy] || sortFunctions.name;
  const sorted = categories.sort(sortFn);
  
  return sortOrder === 'desc' ? sorted.reverse() : sorted;
}

// Helper functions for category metadata
function getCategoryIcon(category) {
  const icons = {
    'JavaScript': '🟨',
    'Python': '🐍',
    'React': '⚛️',
    'Node.js': '🟢',
    'Database': '🗄️',
    'Frontend': '🎨',
    'Backend': '⚙️',
    'DevOps': '🔧',
    'System Design': '🏗️',
    'Data Structures': '📊',
    'Algorithms': '🧮',
    'Testing': '🧪',
    'Security': '🔒'
  };
  return icons[category] || '📝';
}

function getCategoryColor(category) {
  const colors = {
    'JavaScript': '#F7DF1E',
    'Python': '#3776AB',
    'React': '#61DAFB',
    'Node.js': '#339933',
    'Database': '#336791',
    'Frontend': '#FF6B6B',
    'Backend': '#4ECDC4',
    'DevOps': '#FF8C42',
    'System Design': '#9B59B6',
    'Data Structures': '#3498DB',
    'Algorithms': '#E74C3C',
    'Testing': '#2ECC71',
    'Security': '#E67E22'
  };
  return colors[category] || '#95A5A6';
}

function getCategoryDescription(category) {
  const descriptions = {
    'JavaScript': 'Client-side and server-side JavaScript programming',
    'Python': 'Python programming language and frameworks',
    'React': 'React.js library and ecosystem',
    'Node.js': 'Server-side JavaScript runtime',
    'Database': 'Database design, SQL, and NoSQL concepts',
    'Frontend': 'User interface and user experience development',
    'Backend': 'Server-side development and APIs',
    'DevOps': 'Development operations and deployment',
    'System Design': 'Large-scale system architecture',
    'Data Structures': 'Data organization and manipulation',
    'Algorithms': 'Problem-solving and algorithmic thinking',
    'Testing': 'Software testing methodologies',
    'Security': 'Application and system security'
  };
  return descriptions[category] || `Questions related to ${category}`;
}
